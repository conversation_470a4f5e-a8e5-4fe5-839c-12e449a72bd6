package com.library;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 图书管理系统主应用类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@SpringBootApplication
@EnableCaching
@EnableTransactionManagement
@MapperScan("com.library.repository")
public class LibraryManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(LibraryManagementApplication.class, args);
        System.out.println("=================================");
        System.out.println("图书管理系统启动成功！");
        System.out.println("API文档地址: http://localhost:8080/api");
        System.out.println("=================================");
    }
}
