<template>
  <div class="borrows-page">
    <div class="page-header">
      <h1 class="page-title">借阅管理</h1>
      <p class="page-description">管理图书借阅、归还、续借等业务流程</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon size="80" color="#909399">
          <DocumentCopy />
        </el-icon>
        <h2>借阅管理功能</h2>
        <p>此功能正在开发中，敬请期待...</p>
        <div class="feature-list">
          <el-tag v-for="feature in features" :key="feature" type="info" class="feature-tag">
            {{ feature }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
const features = [
  '图书借阅',
  '图书归还',
  '续借申请',
  '逾期管理',
  '借阅记录',
  '统计报表'
]
</script>

<style lang="scss" scoped>
.borrows-page {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h2 {
      margin: 20px 0;
      color: #606266;
    }
    
    p {
      color: #909399;
      margin-bottom: 30px;
    }
    
    .feature-list {
      .feature-tag {
        margin: 5px;
      }
    }
  }
}
</style>
