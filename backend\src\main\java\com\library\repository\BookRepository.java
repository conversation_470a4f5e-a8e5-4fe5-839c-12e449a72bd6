package com.library.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.Book;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 图书数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Mapper
public interface BookRepository extends BaseMapper<Book> {

    /**
     * 根据ISBN查找图书
     * 
     * @param isbn ISBN号
     * @return 图书信息
     */
    @Select("SELECT b.*, c.name as category_name FROM books b " +
            "LEFT JOIN categories c ON b.category_id = c.id " +
            "WHERE b.isbn = #{isbn} AND b.deleted = 0")
    Book findByIsbn(@Param("isbn") String isbn);

    /**
     * 检查ISBN是否存在
     * 
     * @param isbn ISBN号
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM books WHERE isbn = #{isbn} AND deleted = 0")
    boolean existsByIsbn(@Param("isbn") String isbn);

    /**
     * 分页查询图书列表（带分类信息）
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param categoryId 分类ID
     * @param author 作者
     * @param status 图书状态
     * @return 图书分页列表
     */
    IPage<Book> findBooksWithConditions(
        Page<Book> page,
        @Param("keyword") String keyword,
        @Param("categoryId") Long categoryId,
        @Param("author") String author,
        @Param("status") Integer status
    );

    /**
     * 根据分类ID查询图书列表
     * 
     * @param categoryId 分类ID
     * @return 图书列表
     */
    @Select("SELECT b.*, c.name as category_name FROM books b " +
            "LEFT JOIN categories c ON b.category_id = c.id " +
            "WHERE b.category_id = #{categoryId} AND b.status = 1 AND b.deleted = 0")
    List<Book> findByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 查询可借阅图书列表
     * 
     * @return 可借阅图书列表
     */
    @Select("SELECT b.*, c.name as category_name FROM books b " +
            "LEFT JOIN categories c ON b.category_id = c.id " +
            "WHERE b.available_quantity > 0 AND b.status = 1 AND b.deleted = 0")
    List<Book> findAvailableBooks();

    /**
     * 更新图书可借数量
     * 
     * @param bookId 图书ID
     * @param quantity 数量变化（正数增加，负数减少）
     * @return 更新行数
     */
    @Update("UPDATE books SET available_quantity = available_quantity + #{quantity} " +
            "WHERE id = #{bookId} AND deleted = 0")
    int updateAvailableQuantity(@Param("bookId") Long bookId, @Param("quantity") Integer quantity);

    /**
     * 根据关键词搜索图书
     * 
     * @param keyword 搜索关键词
     * @return 图书列表
     */
    List<Book> searchBooks(@Param("keyword") String keyword);

    /**
     * 统计图书总数
     * 
     * @return 图书总数
     */
    @Select("SELECT COUNT(*) FROM books WHERE deleted = 0")
    long countBooks();

    /**
     * 根据状态统计图书数量
     * 
     * @param status 图书状态
     * @return 图书数量
     */
    @Select("SELECT COUNT(*) FROM books WHERE status = #{status} AND deleted = 0")
    long countBooksByStatus(@Param("status") Integer status);

    /**
     * 根据分类统计图书数量
     * 
     * @param categoryId 分类ID
     * @return 图书数量
     */
    @Select("SELECT COUNT(*) FROM books WHERE category_id = #{categoryId} AND deleted = 0")
    long countBooksByCategory(@Param("categoryId") Long categoryId);

    /**
     * 统计可借图书数量
     * 
     * @return 可借图书数量
     */
    @Select("SELECT COUNT(*) FROM books WHERE available_quantity > 0 AND status = 1 AND deleted = 0")
    long countAvailableBooks();

    /**
     * 获取热门图书（按借阅次数排序）
     * 
     * @param limit 限制数量
     * @return 热门图书列表
     */
    List<Book> findPopularBooks(@Param("limit") Integer limit);
}
