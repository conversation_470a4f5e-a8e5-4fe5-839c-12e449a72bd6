2025-06-21 20:05:56 [main] INFO  com.library.LibraryManagementApplication - Starting LibraryManagementApplication using Java 21.0.6 with PID 35564 (D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes started by ZSG in D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend)
2025-06-21 20:05:56 [main] DEBUG com.library.LibraryManagementApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21 20:05:56 [main] INFO  com.library.LibraryManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 20:05:57 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 20:05:57 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 20:05:57 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-21 20:05:57 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-06-21 20:05:57 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-21 20:05:57 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:838)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:775)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:597)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.library.LibraryManagementApplication.main(LibraryManagementApplication.java:23)
2025-06-21 20:08:54 [main] INFO  com.library.LibraryManagementApplication - Starting LibraryManagementApplication using Java 21.0.6 with PID 25664 (D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes started by ZSG in D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend)
2025-06-21 20:08:54 [main] DEBUG com.library.LibraryManagementApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21 20:08:54 [main] INFO  com.library.LibraryManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 20:08:54 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-06-21 20:08:54 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-21 20:08:54 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:838)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:775)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:597)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.library.LibraryManagementApplication.main(LibraryManagementApplication.java:21)
2025-06-21 20:09:53 [main] INFO  com.library.LibraryManagementApplication - Starting LibraryManagementApplication using Java 21.0.6 with PID 12036 (D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes started by ZSG in D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend)
2025-06-21 20:09:53 [main] DEBUG com.library.LibraryManagementApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21 20:09:53 [main] INFO  com.library.LibraryManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 20:09:53 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-06-21 20:09:53 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-21 20:09:53 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:838)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:775)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:597)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.library.LibraryManagementApplication.main(LibraryManagementApplication.java:21)
2025-06-21 20:15:32 [main] INFO  com.library.LibraryManagementApplication - Starting LibraryManagementApplication using Java 21.0.6 on DESKTOP-D7V2AED with PID 21820 (D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes started by ZSG in D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend)
2025-06-21 20:15:32 [main] DEBUG com.library.LibraryManagementApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-21 20:15:32 [main] INFO  com.library.LibraryManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 20:15:33 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-21 20:15:33 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 20:15:33 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-21 20:15:33 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-21 20:15:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1145 ms
2025-06-21 20:15:34 [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jwtAuthenticationFilter' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\security\JwtAuthenticationFilter.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\service\impl\UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\config\SecurityConfig.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'jwtAuthenticationFilter': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-21 20:15:34 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-21 20:15:34 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-06-21 20:15:34 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-21 20:15:34 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

┌─────┐
|  jwtAuthenticationFilter defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\security\JwtAuthenticationFilter.class]
↑     ↓
|  userServiceImpl defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\service\impl\UserServiceImpl.class]
↑     ↓
|  securityConfig defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\config\SecurityConfig.class]
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

2025-06-21 20:16:31 [main] INFO  com.library.LibraryManagementApplication - Starting LibraryManagementApplication using Java 21.0.6 on DESKTOP-D7V2AED with PID 21088 (D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes started by ZSG in D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend)
2025-06-21 20:16:31 [main] DEBUG com.library.LibraryManagementApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-21 20:16:31 [main] INFO  com.library.LibraryManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 20:16:32 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-21 20:16:32 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 20:16:32 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-21 20:16:32 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-21 20:16:32 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1092 ms
2025-06-21 20:16:33 [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jwtAuthenticationFilter' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\security\JwtAuthenticationFilter.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\service\impl\UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig' defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\config\SecurityConfig.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'jwtAuthenticationFilter': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-06-21 20:16:33 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-21 20:16:33 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-06-21 20:16:33 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-21 20:16:33 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

┌─────┐
|  jwtAuthenticationFilter defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\security\JwtAuthenticationFilter.class]
↑     ↓
|  userServiceImpl defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\service\impl\UserServiceImpl.class]
↑     ↓
|  securityConfig defined in file [D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend\target\classes\com\library\config\SecurityConfig.class]
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

