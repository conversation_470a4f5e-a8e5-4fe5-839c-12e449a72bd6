package com.library.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 图书实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("books")
public class Book {

    /**
     * 图书ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * ISBN号（唯一）
     */
    @TableField("isbn")
    private String isbn;

    /**
     * 图书标题
     */
    @TableField("title")
    private String title;

    /**
     * 作者
     */
    @TableField("author")
    private String author;

    /**
     * 出版社
     */
    @TableField("publisher")
    private String publisher;

    /**
     * 出版日期
     */
    @TableField("publish_date")
    private LocalDate publishDate;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 分类名称（非数据库字段）
     */
    @TableField(exist = false)
    private String categoryName;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 总数量
     */
    @TableField("total_quantity")
    private Integer totalQuantity;

    /**
     * 可借数量
     */
    @TableField("available_quantity")
    private Integer availableQuantity;

    /**
     * 图书描述
     */
    @TableField("description")
    private String description;

    /**
     * 封面图片URL
     */
    @TableField("cover_image")
    private String coverImage;

    /**
     * 存放位置
     */
    @TableField("location")
    private String location;

    /**
     * 图书状态：1-正常，0-下架
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 检查图书是否可借
     */
    public boolean isAvailable() {
        return Integer.valueOf(1).equals(this.status) && 
               this.availableQuantity != null && 
               this.availableQuantity > 0;
    }

    /**
     * 检查图书是否正常状态
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(this.status);
    }

    /**
     * 获取已借出数量
     */
    public Integer getBorrowedQuantity() {
        if (totalQuantity == null || availableQuantity == null) {
            return 0;
        }
        return totalQuantity - availableQuantity;
    }

    /**
     * 减少可借数量
     */
    public boolean decreaseAvailableQuantity() {
        if (availableQuantity != null && availableQuantity > 0) {
            availableQuantity--;
            return true;
        }
        return false;
    }

    /**
     * 增加可借数量
     */
    public boolean increaseAvailableQuantity() {
        if (availableQuantity != null && totalQuantity != null && 
            availableQuantity < totalQuantity) {
            availableQuantity++;
            return true;
        }
        return false;
    }
}
