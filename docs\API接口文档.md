# 图书管理系统API接口文档

## 1. 接口概述

**Base URL:** `http://localhost:8080/api`  
**API版本:** v1  
**数据格式:** JSON  
**字符编码:** UTF-8  

## 2. 通用响应格式

### 2.1 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-06-21T10:30:00Z"
}
```

### 2.2 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "data": null,
  "timestamp": "2025-06-21T10:30:00Z"
}
```

### 2.3 状态码说明
- `200` - 操作成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 3. 认证接口

### 3.1 用户登录
**接口地址:** `POST /auth/login`

**请求参数:**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应数据:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "role": "ADMIN",
      "email": "<EMAIL>"
    }
  }
}
```

### 3.2 用户注册
**接口地址:** `POST /auth/register`

**请求参数:**
```json
{
  "username": "string",
  "password": "string",
  "realName": "string",
  "email": "string",
  "phone": "string"
}
```

### 3.3 退出登录
**接口地址:** `POST /auth/logout`

**请求头:** `Authorization: Bearer {token}`

## 4. 用户管理接口

### 4.1 获取用户列表
**接口地址:** `GET /users`

**请求参数:**
- `page` (int): 页码，默认1
- `size` (int): 每页大小，默认10
- `keyword` (string): 搜索关键词
- `role` (string): 用户角色筛选

**响应数据:**
```json
{
  "code": 200,
  "data": {
    "content": [
      {
        "id": 1,
        "username": "user001",
        "realName": "张三",
        "email": "<EMAIL>",
        "role": "READER",
        "status": 1,
        "createdAt": "2025-06-21T10:00:00Z"
      }
    ],
    "totalElements": 100,
    "totalPages": 10,
    "currentPage": 1,
    "size": 10
  }
}
```

### 4.2 获取用户详情
**接口地址:** `GET /users/{id}`

### 4.3 创建用户
**接口地址:** `POST /users`

### 4.4 更新用户
**接口地址:** `PUT /users/{id}`

### 4.5 删除用户
**接口地址:** `DELETE /users/{id}`

## 5. 图书管理接口

### 5.1 获取图书列表
**接口地址:** `GET /books`

**请求参数:**
- `page` (int): 页码
- `size` (int): 每页大小
- `keyword` (string): 搜索关键词
- `categoryId` (long): 分类ID
- `author` (string): 作者筛选
- `status` (int): 状态筛选

**响应数据:**
```json
{
  "code": 200,
  "data": {
    "content": [
      {
        "id": 1,
        "isbn": "9787111234567",
        "title": "Java编程思想",
        "author": "Bruce Eckel",
        "publisher": "机械工业出版社",
        "publishDate": "2020-01-01",
        "categoryId": 2,
        "categoryName": "科技",
        "price": 89.00,
        "totalQuantity": 10,
        "availableQuantity": 8,
        "coverImage": "/images/java-thinking.jpg",
        "status": 1
      }
    ],
    "totalElements": 500,
    "totalPages": 50,
    "currentPage": 1
  }
}
```

### 5.2 获取图书详情
**接口地址:** `GET /books/{id}`

### 5.3 创建图书
**接口地址:** `POST /books`

**请求参数:**
```json
{
  "isbn": "string",
  "title": "string",
  "author": "string",
  "publisher": "string",
  "publishDate": "2025-01-01",
  "categoryId": 1,
  "price": 59.90,
  "totalQuantity": 5,
  "description": "string",
  "location": "A区1层001"
}
```

### 5.4 更新图书
**接口地址:** `PUT /books/{id}`

### 5.5 删除图书
**接口地址:** `DELETE /books/{id}`

## 6. 图书分类接口

### 6.1 获取分类列表
**接口地址:** `GET /categories`

**响应数据:**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "文学",
      "code": "LIT",
      "description": "文学类图书",
      "parentId": null,
      "sortOrder": 1,
      "status": 1
    }
  ]
}
```

### 6.2 创建分类
**接口地址:** `POST /categories`

### 6.3 更新分类
**接口地址:** `PUT /categories/{id}`

### 6.4 删除分类
**接口地址:** `DELETE /categories/{id}`

## 7. 借阅管理接口

### 7.1 借阅图书
**接口地址:** `POST /borrows`

**请求参数:**
```json
{
  "bookId": 1,
  "userId": 2,
  "borrowDays": 30
}
```

### 7.2 归还图书
**接口地址:** `PUT /borrows/{id}/return`

### 7.3 续借图书
**接口地址:** `PUT /borrows/{id}/renew`

**请求参数:**
```json
{
  "renewDays": 15
}
```

### 7.4 获取借阅记录
**接口地址:** `GET /borrows`

**请求参数:**
- `userId` (long): 用户ID
- `bookId` (long): 图书ID
- `status` (string): 借阅状态
- `startDate` (string): 开始日期
- `endDate` (string): 结束日期

## 8. 预约管理接口

### 8.1 预约图书
**接口地址:** `POST /reservations`

**请求参数:**
```json
{
  "bookId": 1,
  "userId": 2
}
```

### 8.2 取消预约
**接口地址:** `DELETE /reservations/{id}`

### 8.3 获取预约记录
**接口地址:** `GET /reservations`

## 9. 统计报表接口

### 9.1 借阅统计
**接口地址:** `GET /statistics/borrows`

**请求参数:**
- `startDate` (string): 开始日期
- `endDate` (string): 结束日期
- `groupBy` (string): 分组方式 (day/month/year)

### 9.2 图书统计
**接口地址:** `GET /statistics/books`

### 9.3 用户统计
**接口地址:** `GET /statistics/users`

## 10. 文件上传接口

### 10.1 上传图书封面
**接口地址:** `POST /upload/book-cover`

**请求类型:** `multipart/form-data`

**响应数据:**
```json
{
  "code": 200,
  "data": {
    "url": "/images/covers/book_123456.jpg",
    "filename": "book_123456.jpg",
    "size": 102400
  }
}
```

## 11. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名或密码错误 |
| 1002 | 用户已存在 |
| 1003 | 用户不存在 |
| 2001 | 图书不存在 |
| 2002 | 图书库存不足 |
| 2003 | ISBN已存在 |
| 3001 | 借阅记录不存在 |
| 3002 | 图书已被借出 |
| 3003 | 超出借阅限制 |
| 4001 | 分类不存在 |
| 4002 | 分类下存在图书，无法删除 |
