package com.library.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.library.entity.Book;

import java.util.List;

/**
 * 图书服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
public interface BookService {

    /**
     * 分页查询图书列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param categoryId 分类ID
     * @param author 作者
     * @param status 图书状态
     * @return 图书分页列表
     */
    IPage<Book> findBooks(Integer page, Integer size, String keyword, Long categoryId, String author, Integer status);

    /**
     * 根据ID查找图书
     * 
     * @param bookId 图书ID
     * @return 图书信息
     */
    Book findById(Long bookId);

    /**
     * 根据ISBN查找图书
     * 
     * @param isbn ISBN号
     * @return 图书信息
     */
    Book findByIsbn(String isbn);

    /**
     * 创建图书
     * 
     * @param book 图书信息
     * @return 创建的图书
     */
    Book createBook(Book book);

    /**
     * 更新图书信息
     * 
     * @param bookId 图书ID
     * @param book 图书信息
     * @return 更新的图书
     */
    Book updateBook(Long bookId, Book book);

    /**
     * 删除图书
     * 
     * @param bookId 图书ID
     * @return 删除结果
     */
    boolean deleteBook(Long bookId);

    /**
     * 更新图书状态
     * 
     * @param bookId 图书ID
     * @param status 状态
     * @return 操作结果
     */
    boolean updateBookStatus(Long bookId, Integer status);

    /**
     * 更新图书库存
     * 
     * @param bookId 图书ID
     * @param quantity 数量变化（正数增加，负数减少）
     * @return 操作结果
     */
    boolean updateBookQuantity(Long bookId, Integer quantity);

    /**
     * 检查图书是否可借
     * 
     * @param bookId 图书ID
     * @return 是否可借
     */
    boolean isBookAvailable(Long bookId);

    /**
     * 搜索图书
     * 
     * @param keyword 搜索关键词
     * @return 图书列表
     */
    List<Book> searchBooks(String keyword);

    /**
     * 获取可借阅图书列表
     * 
     * @return 可借阅图书列表
     */
    List<Book> getAvailableBooks();

    /**
     * 根据分类获取图书列表
     * 
     * @param categoryId 分类ID
     * @return 图书列表
     */
    List<Book> getBooksByCategory(Long categoryId);

    /**
     * 获取热门图书
     * 
     * @param limit 限制数量
     * @return 热门图书列表
     */
    List<Book> getPopularBooks(Integer limit);

    /**
     * 检查ISBN是否存在
     * 
     * @param isbn ISBN号
     * @return 是否存在
     */
    boolean existsByIsbn(String isbn);

    /**
     * 获取图书统计信息
     * 
     * @return 统计信息
     */
    BookStatistics getBookStatistics();

    /**
     * 图书统计信息类
     */
    class BookStatistics {
        private long totalBooks;
        private long availableBooks;
        private long borrowedBooks;
        private long categoryCount;

        public BookStatistics(long totalBooks, long availableBooks, long borrowedBooks, long categoryCount) {
            this.totalBooks = totalBooks;
            this.availableBooks = availableBooks;
            this.borrowedBooks = borrowedBooks;
            this.categoryCount = categoryCount;
        }

        // Getters
        public long getTotalBooks() { return totalBooks; }
        public long getAvailableBooks() { return availableBooks; }
        public long getBorrowedBooks() { return borrowedBooks; }
        public long getCategoryCount() { return categoryCount; }
    }
}
