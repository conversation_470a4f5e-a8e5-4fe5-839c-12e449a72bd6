package com.library.controller;

import com.library.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "图书管理系统运行正常");
        return Result.success(data);
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "图书管理系统");
        data.put("version", "1.0.0");
        data.put("description", "基于Spring Boot + Vue.js的图书管理系统");
        data.put("author", "System");
        return Result.success(data);
    }
}
