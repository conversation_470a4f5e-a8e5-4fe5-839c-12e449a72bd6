<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="not-found-icon">
        <el-icon size="120" color="#909399">
          <DocumentDelete />
        </el-icon>
      </div>
      <h1 class="not-found-title">404</h1>
      <p class="not-found-description">抱歉，您访问的页面不存在</p>
      <div class="not-found-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><House /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.not-found-content {
  text-align: center;
  
  .not-found-icon {
    margin-bottom: 20px;
  }
  
  .not-found-title {
    font-size: 72px;
    font-weight: 600;
    color: #909399;
    margin: 20px 0;
  }
  
  .not-found-description {
    font-size: 18px;
    color: #606266;
    margin-bottom: 30px;
  }
  
  .not-found-actions {
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
