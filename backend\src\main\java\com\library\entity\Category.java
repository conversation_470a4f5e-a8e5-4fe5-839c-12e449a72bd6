package com.library.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图书分类实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("categories")
public class Category {

    /**
     * 分类ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 分类名称
     */
    @TableField("name")
    private String name;

    /**
     * 分类编码（唯一）
     */
    @TableField("code")
    private String code;

    /**
     * 分类描述
     */
    @TableField("description")
    private String description;

    /**
     * 父分类ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 子分类列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<Category> children;

    /**
     * 父分类名称（非数据库字段）
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 图书数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer bookCount;

    /**
     * 检查分类是否启用
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(this.status);
    }

    /**
     * 检查是否为根分类
     */
    public boolean isRoot() {
        return this.parentId == null;
    }

    /**
     * 检查是否有子分类
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }
}
