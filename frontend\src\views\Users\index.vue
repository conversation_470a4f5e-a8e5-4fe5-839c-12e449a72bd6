<template>
  <div class="users-page">
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-description">管理系统用户，包括管理员、图书管理员和普通读者</p>
    </div>
    
    <el-card>
      <div class="admin-only">
        <el-icon size="80" color="#409EFF">
          <User />
        </el-icon>
        <h2>用户管理功能</h2>
        <p>仅限系统管理员访问</p>
        <div class="feature-list">
          <el-tag v-for="feature in features" :key="feature" type="primary" class="feature-tag">
            {{ feature }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
const features = [
  '用户注册审核',
  '角色权限管理',
  '用户状态控制',
  '密码重置',
  '用户统计',
  '操作日志'
]
</script>

<style lang="scss" scoped>
.users-page {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .admin-only {
    text-align: center;
    padding: 60px 20px;
    
    h2 {
      margin: 20px 0;
      color: #606266;
    }
    
    p {
      color: #909399;
      margin-bottom: 30px;
    }
    
    .feature-list {
      .feature-tag {
        margin: 5px;
      }
    }
  }
}
</style>
