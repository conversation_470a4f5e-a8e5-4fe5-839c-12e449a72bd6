package com.library.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Mapper
public interface UserRepository extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND deleted = 0")
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND deleted = 0")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param role 用户角色
     * @param status 用户状态
     * @return 用户分页列表
     */
    IPage<User> findUsersWithConditions(
        Page<User> page, 
        @Param("keyword") String keyword,
        @Param("role") String role,
        @Param("status") Integer status
    );

    /**
     * 根据角色查询用户列表
     * 
     * @param role 用户角色
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE role = #{role} AND status = 1 AND deleted = 0")
    List<User> findByRole(@Param("role") String role);

    /**
     * 统计用户数量
     * 
     * @return 用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE deleted = 0")
    long countUsers();

    /**
     * 根据状态统计用户数量
     * 
     * @param status 用户状态
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = #{status} AND deleted = 0")
    long countUsersByStatus(@Param("status") Integer status);

    /**
     * 根据角色统计用户数量
     * 
     * @param role 用户角色
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE role = #{role} AND deleted = 0")
    long countUsersByRole(@Param("role") String role);
}
