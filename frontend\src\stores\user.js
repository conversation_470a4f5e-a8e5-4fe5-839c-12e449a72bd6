import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getUserInfo } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)

  // 登录
  const userLogin = async (loginForm) => {
    try {
      const response = await login(loginForm)
      if (response.code === 200) {
        token.value = response.data.token
        userInfo.value = response.data.user
        localStorage.setItem('token', token.value)
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return false
    }
  }

  // 退出登录
  const userLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      ElMessage.success('退出登录成功')
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await getUserInfo()
      if (response.code === 200) {
        userInfo.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 初始化用户信息
  const initUser = async () => {
    if (token.value && !userInfo.value) {
      try {
        await fetchUserInfo()
      } catch (error) {
        // 如果获取用户信息失败，清除token
        token.value = ''
        localStorage.removeItem('token')
      }
    }
  }

  // 检查权限
  const hasPermission = (roles) => {
    if (!userInfo.value) return false
    if (!roles || roles.length === 0) return true
    return roles.includes(userInfo.value.role)
  }

  // 检查是否为管理员
  const isAdmin = () => {
    return userInfo.value?.role === 'ADMIN'
  }

  // 检查是否为图书管理员
  const isLibrarian = () => {
    return userInfo.value?.role === 'LIBRARIAN'
  }

  // 检查是否为读者
  const isReader = () => {
    return userInfo.value?.role === 'READER'
  }

  return {
    token,
    userInfo,
    login: userLogin,
    logout: userLogout,
    getUserInfo: fetchUserInfo,
    initUser,
    hasPermission,
    isAdmin,
    isLibrarian,
    isReader
  }
})
