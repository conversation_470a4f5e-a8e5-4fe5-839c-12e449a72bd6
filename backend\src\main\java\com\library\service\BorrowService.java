package com.library.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.library.entity.BorrowRecord;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 借阅服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
public interface BorrowService {

    /**
     * 借阅图书
     * 
     * @param userId 用户ID
     * @param bookId 图书ID
     * @param librarianId 管理员ID
     * @param borrowDays 借阅天数
     * @return 借阅记录
     */
    BorrowRecord borrowBook(Long userId, Long bookId, Long librarianId, Integer borrowDays);

    /**
     * 归还图书
     * 
     * @param recordId 借阅记录ID
     * @param librarianId 管理员ID
     * @return 操作结果
     */
    boolean returnBook(Long recordId, Long librarianId);

    /**
     * 续借图书
     * 
     * @param recordId 借阅记录ID
     * @param renewDays 续借天数
     * @return 操作结果
     */
    boolean renewBook(Long recordId, Integer renewDays);

    /**
     * 分页查询借阅记录
     * 
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @param bookId 图书ID
     * @param status 借阅状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 借阅记录分页列表
     */
    IPage<BorrowRecord> findBorrowRecords(Integer page, Integer size, Long userId, Long bookId, 
                                         String status, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据ID查找借阅记录
     * 
     * @param recordId 记录ID
     * @return 借阅记录
     */
    BorrowRecord findById(Long recordId);

    /**
     * 获取用户当前借阅记录
     * 
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    List<BorrowRecord> getCurrentBorrowsByUser(Long userId);

    /**
     * 获取图书当前借阅记录
     * 
     * @param bookId 图书ID
     * @return 借阅记录列表
     */
    List<BorrowRecord> getCurrentBorrowsByBook(Long bookId);

    /**
     * 获取逾期借阅记录
     * 
     * @return 逾期记录列表
     */
    List<BorrowRecord> getOverdueRecords();

    /**
     * 获取用户逾期记录
     * 
     * @param userId 用户ID
     * @return 逾期记录列表
     */
    List<BorrowRecord> getOverdueRecordsByUser(Long userId);

    /**
     * 检查用户是否可以借阅
     * 
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 检查结果
     */
    BorrowCheckResult checkBorrowEligibility(Long userId, Long bookId);

    /**
     * 检查是否可以续借
     * 
     * @param recordId 借阅记录ID
     * @return 是否可以续借
     */
    boolean canRenew(Long recordId);

    /**
     * 处理逾期记录
     * 
     * @return 处理的记录数
     */
    int processOverdueRecords();

    /**
     * 计算罚金
     * 
     * @param recordId 借阅记录ID
     * @return 罚金金额
     */
    java.math.BigDecimal calculateFine(Long recordId);

    /**
     * 获取借阅统计信息
     * 
     * @return 统计信息
     */
    BorrowStatistics getBorrowStatistics();

    /**
     * 借阅检查结果
     */
    class BorrowCheckResult {
        private boolean canBorrow;
        private String reason;
        private int currentBorrowCount;
        private int maxBorrowLimit;

        public BorrowCheckResult(boolean canBorrow, String reason, int currentBorrowCount, int maxBorrowLimit) {
            this.canBorrow = canBorrow;
            this.reason = reason;
            this.currentBorrowCount = currentBorrowCount;
            this.maxBorrowLimit = maxBorrowLimit;
        }

        // Getters
        public boolean isCanBorrow() { return canBorrow; }
        public String getReason() { return reason; }
        public int getCurrentBorrowCount() { return currentBorrowCount; }
        public int getMaxBorrowLimit() { return maxBorrowLimit; }
    }

    /**
     * 借阅统计信息
     */
    class BorrowStatistics {
        private long totalBorrows;
        private long currentBorrows;
        private long overdueRecords;
        private long todayBorrows;
        private long todayReturns;

        public BorrowStatistics(long totalBorrows, long currentBorrows, long overdueRecords, 
                               long todayBorrows, long todayReturns) {
            this.totalBorrows = totalBorrows;
            this.currentBorrows = currentBorrows;
            this.overdueRecords = overdueRecords;
            this.todayBorrows = todayBorrows;
            this.todayReturns = todayReturns;
        }

        // Getters
        public long getTotalBorrows() { return totalBorrows; }
        public long getCurrentBorrows() { return currentBorrows; }
        public long getOverdueRecords() { return overdueRecords; }
        public long getTodayBorrows() { return todayBorrows; }
        public long getTodayReturns() { return todayReturns; }
    }
}
