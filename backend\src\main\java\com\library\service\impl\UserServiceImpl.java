package com.library.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.common.ResultCode;
import com.library.entity.User;
import com.library.exception.BusinessException;
import com.library.repository.UserRepository;
import com.library.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public User register(User user) {
        log.info("用户注册: {}", user.getUsername());
        
        // 验证用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }
        
        // 验证邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && existsByEmail(user.getEmail())) {
            throw new BusinessException(ResultCode.EMAIL_ALREADY_EXISTS);
        }
        
        // 设置默认值
        user.setRole(User.UserRole.READER); // 默认为普通读者
        user.setStatus(1); // 默认启用
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 保存用户
        userRepository.insert(user);
        
        log.info("用户注册成功: {}", user.getUsername());
        return user;
    }

    @Override
    public User login(String username, String password) {
        log.info("用户登录: {}", username);
        
        User user = findByUsername(username);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        if (!user.isActive()) {
            throw new BusinessException(ResultCode.USER_DISABLED);
        }
        
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(ResultCode.USERNAME_PASSWORD_ERROR);
        }
        
        log.info("用户登录成功: {}", username);
        return user;
    }

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public User findById(Long userId) {
        User user = userRepository.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return user;
    }

    @Override
    public IPage<User> findUsers(Integer page, Integer size, String keyword, String role, Integer status) {
        Page<User> pageParam = new Page<>(page, size);
        return userRepository.findUsersWithConditions(pageParam, keyword, role, status);
    }

    @Override
    @Transactional
    public User createUser(User user) {
        log.info("创建用户: {}", user.getUsername());
        
        // 验证用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }
        
        // 验证邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && existsByEmail(user.getEmail())) {
            throw new BusinessException(ResultCode.EMAIL_ALREADY_EXISTS);
        }
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认状态
        if (user.getStatus() == null) {
            user.setStatus(1);
        }
        
        // 保存用户
        userRepository.insert(user);
        
        log.info("用户创建成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional
    public User updateUser(Long userId, User user) {
        log.info("更新用户: {}", userId);
        
        User existingUser = findById(userId);
        
        // 更新基本信息
        if (StringUtils.hasText(user.getRealName())) {
            existingUser.setRealName(user.getRealName());
        }
        if (StringUtils.hasText(user.getEmail())) {
            // 检查邮箱是否被其他用户使用
            User emailUser = userRepository.findByEmail(user.getEmail());
            if (emailUser != null && !emailUser.getId().equals(userId)) {
                throw new BusinessException(ResultCode.EMAIL_ALREADY_EXISTS);
            }
            existingUser.setEmail(user.getEmail());
        }
        if (StringUtils.hasText(user.getPhone())) {
            existingUser.setPhone(user.getPhone());
        }
        if (user.getRole() != null) {
            existingUser.setRole(user.getRole());
        }
        
        // 更新用户
        userRepository.updateById(existingUser);
        
        log.info("用户更新成功: {}", userId);
        return existingUser;
    }

    @Override
    @Transactional
    public boolean deleteUser(Long userId) {
        log.info("删除用户: {}", userId);
        
        User user = findById(userId);
        
        // 管理员不能删除自己
        // TODO: 获取当前登录用户ID进行比较
        
        int result = userRepository.deleteById(userId);
        
        log.info("用户删除{}: {}", result > 0 ? "成功" : "失败", userId);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateUserStatus(Long userId, Integer status) {
        log.info("更新用户状态: {} -> {}", userId, status);
        
        User user = findById(userId);
        user.setStatus(status);
        
        int result = userRepository.updateById(user);
        
        log.info("用户状态更新{}: {}", result > 0 ? "成功" : "失败", userId);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("修改密码: {}", userId);
        
        User user = findById(userId);
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(ResultCode.USERNAME_PASSWORD_ERROR);
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        int result = userRepository.updateById(user);
        
        log.info("密码修改{}: {}", result > 0 ? "成功" : "失败", userId);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean resetPassword(Long userId, String newPassword) {
        log.info("重置密码: {}", userId);
        
        User user = findById(userId);
        user.setPassword(passwordEncoder.encode(newPassword));
        
        int result = userRepository.updateById(user);
        
        log.info("密码重置{}: {}", result > 0 ? "成功" : "失败", userId);
        return result > 0;
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public boolean hasPermission(Long userId, User.UserRole requiredRole) {
        User user = findById(userId);
        
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            return true;
        }
        
        // 图书管理员拥有读者权限
        if (user.isLibrarian() && User.UserRole.READER.equals(requiredRole)) {
            return true;
        }
        
        // 角色匹配
        return requiredRole.equals(user.getRole());
    }

    @Override
    public UserStatistics getUserStatistics() {
        long totalUsers = userRepository.countUsers();
        long activeUsers = userRepository.countUsersByStatus(1);
        long adminUsers = userRepository.countUsersByRole(User.UserRole.ADMIN.getCode());
        long librarianUsers = userRepository.countUsersByRole(User.UserRole.LIBRARIAN.getCode());
        long readerUsers = userRepository.countUsersByRole(User.UserRole.READER.getCode());
        
        return new UserStatistics(totalUsers, activeUsers, adminUsers, librarianUsers, readerUsers);
    }
}
