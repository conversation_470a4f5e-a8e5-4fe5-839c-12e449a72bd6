# 图书管理系统运行指南

## 环境检查

您的Java环境已确认：
- ✅ Java 21.0.6 已安装

还需要检查以下工具：

### 1. 检查Maven
打开命令提示符（cmd）或PowerShell，运行：
```bash
mvn --version
```

### 2. 检查Node.js
```bash
node --version
npm --version
```

### 3. 检查MySQL
```bash
mysql --version
```

## 运行步骤

### 第一步：初始化数据库

1. 启动MySQL服务
2. 打开命令行，连接MySQL：
```bash
mysql -u root -p
```

3. 执行数据库初始化脚本：
```sql
source D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\database\init.sql
```

或者直接复制 `database/init.sql` 文件内容到MySQL客户端执行。

### 第二步：启动后端服务

1. 打开命令提示符
2. 进入后端目录：
```bash
cd "D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\backend"
```

3. 编译项目：
```bash
mvn clean compile
```

4. 启动服务：
```bash
mvn spring-boot:run
```

**预期结果：**
- 控制台显示 "图书管理系统启动成功！"
- 服务运行在 http://localhost:8080

### 第三步：启动前端应用

1. 打开新的命令提示符窗口
2. 进入前端目录：
```bash
cd "D:\study\college\junior2\SystemAnalysisAndDesign\大作业\system\frontend"
```

3. 安装依赖：
```bash
npm install
```

4. 启动开发服务器：
```bash
npm run dev
```

**预期结果：**
- 前端应用运行在 http://localhost:3000
- 浏览器自动打开登录页面

### 第四步：测试系统

1. 访问前端：http://localhost:3000
2. 使用默认账户登录：
   - 用户名：admin
   - 密码：123456

3. 测试后端API：http://localhost:8080/test/health

## 常见问题解决

### 问题1：Maven命令不识别
**解决方案：**
- 确认Maven已安装并配置环境变量
- 重启命令提示符
- 使用完整路径运行Maven

### 问题2：端口被占用
**解决方案：**
- 修改 `backend/src/main/resources/application.yml` 中的端口
- 或者停止占用端口的程序

### 问题3：数据库连接失败
**解决方案：**
- 确认MySQL服务已启动
- 检查 `application.yml` 中的数据库配置
- 确认数据库用户名密码正确

### 问题4：前端依赖安装失败
**解决方案：**
- 使用淘宝镜像：`npm config set registry https://registry.npmmirror.com`
- 清除缓存：`npm cache clean --force`
- 删除 node_modules 重新安装

## 验证成功标志

### 后端启动成功：
```
=================================
图书管理系统启动成功！
API文档地址: http://localhost:8080/api
=================================
```

### 前端启动成功：
```
Local:   http://localhost:3000/
Network: use --host to expose
```

### 系统功能测试：
1. ✅ 登录功能正常
2. ✅ 页面导航正常
3. ✅ 图书列表显示
4. ✅ 用户权限控制

## 开发工具推荐

如果需要查看和修改代码：

### 后端开发：
- **IntelliJ IDEA**：打开 `backend` 文件夹
- **VS Code**：安装Java扩展包

### 前端开发：
- **VS Code**：安装Vue扩展
- **WebStorm**：专业前端IDE

### 数据库管理：
- **MySQL Workbench**：官方图形化工具
- **Navicat**：第三方数据库管理工具

## 项目文档

- 📖 **完整分析报告**：`docs/系统分析设计报告.md`
- 🗄️ **数据库设计**：`docs/数据库设计.md`
- 🔌 **API接口文档**：`docs/API接口文档.md`
- 📋 **项目说明**：`README.md`

---

**如果遇到问题，请按照错误信息逐步排查，或者查看控制台输出的详细错误信息。**
