<template>
  <div class="categories-page">
    <div class="page-header">
      <h1 class="page-title">分类管理</h1>
      <p class="page-description">管理图书分类，支持层级分类结构</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon size="80" color="#67C23A">
          <Menu />
        </el-icon>
        <h2>分类管理功能</h2>
        <p>此功能正在开发中，敬请期待...</p>
        <div class="feature-list">
          <el-tag v-for="feature in features" :key="feature" type="success" class="feature-tag">
            {{ feature }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
const features = [
  '分类创建',
  '层级管理',
  '分类编辑',
  '分类删除',
  '排序调整',
  '状态控制'
]
</script>

<style lang="scss" scoped>
.categories-page {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h2 {
      margin: 20px 0;
      color: #606266;
    }
    
    p {
      color: #909399;
      margin-bottom: 30px;
    }
    
    .feature-list {
      .feature-tag {
        margin: 5px;
      }
    }
  }
}
</style>
