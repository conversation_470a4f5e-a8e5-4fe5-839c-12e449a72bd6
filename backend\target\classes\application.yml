server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: library-management-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: LibraryHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # Redis配置（暂时禁用）
  # data:
  #   redis:
  #     host: localhost
  #     port: 6379
  #     password:
  #     database: 0
  #     timeout: 10000ms
  #     lettuce:
  #       pool:
  #         max-active: 8
  #         max-wait: -1ms
  #         max-idle: 8
  #         min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    com.library: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/library-management.log

# 自定义配置
library:
  # JWT配置
  jwt:
    secret: library-management-system-jwt-secret-key-2025
    expiration: 86400000 # 24小时，单位毫秒
    header: Authorization
    prefix: Bearer 
  
  # 借阅配置
  borrow:
    max-books-per-user: 5 # 每个用户最多借阅图书数量
    default-borrow-days: 30 # 默认借阅天数
    max-renew-times: 2 # 最大续借次数
    fine-per-day: 1.00 # 每天罚金金额
  
  # 文件上传配置
  upload:
    path: /uploads/
    allowed-types: jpg,jpeg,png,gif
    max-size: 5242880 # 5MB

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
