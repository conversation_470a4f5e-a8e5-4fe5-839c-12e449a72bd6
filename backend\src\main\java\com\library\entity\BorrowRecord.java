package com.library.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 借阅记录实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("borrow_records")
public class BorrowRecord {

    /**
     * 借阅记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 图书ID
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 借阅日期
     */
    @TableField("borrow_date")
    private LocalDateTime borrowDate;

    /**
     * 应还日期
     */
    @TableField("due_date")
    private LocalDateTime dueDate;

    /**
     * 实际归还日期
     */
    @TableField("return_date")
    private LocalDateTime returnDate;

    /**
     * 续借次数
     */
    @TableField("renew_count")
    private Integer renewCount;

    /**
     * 罚金金额
     */
    @TableField("fine_amount")
    private BigDecimal fineAmount;

    /**
     * 借阅状态
     */
    @TableField("status")
    private BorrowStatus status;

    /**
     * 办理管理员ID
     */
    @TableField("librarian_id")
    private Long librarianId;

    /**
     * 备注信息
     */
    @TableField("notes")
    private String notes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 用户信息（非数据库字段）
     */
    @TableField(exist = false)
    private User user;

    /**
     * 图书信息（非数据库字段）
     */
    @TableField(exist = false)
    private Book book;

    /**
     * 管理员信息（非数据库字段）
     */
    @TableField(exist = false)
    private User librarian;

    /**
     * 借阅状态枚举
     */
    public enum BorrowStatus {
        /**
         * 借阅中
         */
        BORROWED("BORROWED", "借阅中"),
        
        /**
         * 已归还
         */
        RETURNED("RETURNED", "已归还"),
        
        /**
         * 逾期中
         */
        OVERDUE("OVERDUE", "逾期中");

        private final String code;
        private final String description;

        BorrowStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否逾期
     */
    public boolean isOverdue() {
        if (returnDate != null) {
            return false; // 已归还
        }
        return LocalDateTime.now().isAfter(dueDate);
    }

    /**
     * 计算逾期天数
     */
    public long getOverdueDays() {
        if (!isOverdue()) {
            return 0;
        }
        return ChronoUnit.DAYS.between(dueDate, LocalDateTime.now());
    }

    /**
     * 计算应付罚金
     */
    public BigDecimal calculateFine(BigDecimal finePerDay) {
        long overdueDays = getOverdueDays();
        if (overdueDays <= 0) {
            return BigDecimal.ZERO;
        }
        return finePerDay.multiply(BigDecimal.valueOf(overdueDays));
    }

    /**
     * 检查是否可以续借
     */
    public boolean canRenew(int maxRenewTimes) {
        return BorrowStatus.BORROWED.equals(this.status) && 
               !isOverdue() && 
               (this.renewCount == null || this.renewCount < maxRenewTimes);
    }

    /**
     * 检查是否已归还
     */
    public boolean isReturned() {
        return BorrowStatus.RETURNED.equals(this.status);
    }

    /**
     * 检查是否借阅中
     */
    public boolean isBorrowed() {
        return BorrowStatus.BORROWED.equals(this.status);
    }
}
