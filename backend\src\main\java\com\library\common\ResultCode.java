package com.library.common;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),

    // 用户相关状态码 (1000-1999)
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USERNAME_PASSWORD_ERROR(1003, "用户名或密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    EMAIL_ALREADY_EXISTS(1005, "邮箱已存在"),
    INVALID_TOKEN(1006, "无效的令牌"),
    TOKEN_EXPIRED(1007, "令牌已过期"),
    PERMISSION_DENIED(1008, "权限不足"),

    // 图书相关状态码 (2000-2999)
    BOOK_NOT_FOUND(2001, "图书不存在"),
    BOOK_ALREADY_EXISTS(2002, "图书已存在"),
    ISBN_ALREADY_EXISTS(2003, "ISBN已存在"),
    BOOK_NOT_AVAILABLE(2004, "图书不可借阅"),
    BOOK_OUT_OF_STOCK(2005, "图书库存不足"),
    BOOK_DISABLED(2006, "图书已下架"),

    // 借阅相关状态码 (3000-3999)
    BORROW_RECORD_NOT_FOUND(3001, "借阅记录不存在"),
    BOOK_ALREADY_BORROWED(3002, "图书已被借出"),
    EXCEED_BORROW_LIMIT(3003, "超出借阅限制"),
    BOOK_OVERDUE(3004, "图书已逾期"),
    CANNOT_RENEW(3005, "无法续借"),
    EXCEED_RENEW_LIMIT(3006, "超出续借次数限制"),
    BOOK_NOT_BORROWED(3007, "图书未被借阅"),
    ALREADY_RETURNED(3008, "图书已归还"),

    // 预约相关状态码 (4000-4999)
    RESERVATION_NOT_FOUND(4001, "预约记录不存在"),
    ALREADY_RESERVED(4002, "图书已被预约"),
    RESERVATION_EXPIRED(4003, "预约已过期"),
    RESERVATION_CANCELLED(4004, "预约已取消"),
    CANNOT_RESERVE_AVAILABLE_BOOK(4005, "可借图书无需预约"),

    // 分类相关状态码 (5000-5999)
    CATEGORY_NOT_FOUND(5001, "分类不存在"),
    CATEGORY_ALREADY_EXISTS(5002, "分类已存在"),
    CATEGORY_CODE_EXISTS(5003, "分类编码已存在"),
    CATEGORY_HAS_BOOKS(5004, "分类下存在图书，无法删除"),
    CATEGORY_HAS_CHILDREN(5005, "分类下存在子分类，无法删除"),

    // 文件相关状态码 (6000-6999)
    FILE_UPLOAD_ERROR(6001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(6002, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(6003, "文件大小超出限制"),
    FILE_NOT_FOUND(6004, "文件不存在"),

    // 系统相关状态码 (9000-9999)
    SYSTEM_ERROR(9001, "系统内部错误"),
    DATABASE_ERROR(9002, "数据库操作失败"),
    NETWORK_ERROR(9003, "网络连接异常"),
    SERVICE_UNAVAILABLE(9004, "服务暂不可用");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }
}
