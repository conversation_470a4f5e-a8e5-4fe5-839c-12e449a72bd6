package com.library.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
public interface UserService {

    /**
     * 用户注册
     * 
     * @param user 用户信息
     * @return 注册结果
     */
    User register(User user);

    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 用户信息
     */
    User login(String username, String password);

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    /**
     * 根据用户ID查找用户
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    User findById(Long userId);

    /**
     * 分页查询用户列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param role 用户角色
     * @param status 用户状态
     * @return 用户分页列表
     */
    IPage<User> findUsers(Integer page, Integer size, String keyword, String role, Integer status);

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);

    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param user 用户信息
     * @return 更新的用户
     */
    User updateUser(Long userId, User user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteUser(Long userId);

    /**
     * 启用/禁用用户
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 操作结果
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 重置结果
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 验证用户权限
     * 
     * @param userId 用户ID
     * @param requiredRole 所需角色
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, User.UserRole requiredRole);

    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    UserStatistics getUserStatistics();

    /**
     * 用户统计信息类
     */
    class UserStatistics {
        private long totalUsers;
        private long activeUsers;
        private long adminUsers;
        private long librarianUsers;
        private long readerUsers;

        // 构造函数
        public UserStatistics(long totalUsers, long activeUsers, long adminUsers, 
                            long librarianUsers, long readerUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.adminUsers = adminUsers;
            this.librarianUsers = librarianUsers;
            this.readerUsers = readerUsers;
        }

        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getAdminUsers() { return adminUsers; }
        public long getLibrarianUsers() { return librarianUsers; }
        public long getReaderUsers() { return readerUsers; }
    }
}
