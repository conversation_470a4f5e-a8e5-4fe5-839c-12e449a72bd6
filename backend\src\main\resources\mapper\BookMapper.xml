<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.library.repository.BookRepository">

    <!-- 分页查询图书列表 -->
    <select id="findBooksWithConditions" resultType="com.library.entity.Book">
        SELECT b.*, c.name as category_name 
        FROM books b
        LEFT JOIN categories c ON b.category_id = c.id
        WHERE b.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (b.title LIKE CONCAT('%', #{keyword}, '%') 
                 OR b.author LIKE CONCAT('%', #{keyword}, '%')
                 OR b.isbn LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categoryId != null">
            AND b.category_id = #{categoryId}
        </if>
        <if test="author != null and author != ''">
            AND b.author LIKE CONCAT('%', #{author}, '%')
        </if>
        <if test="status != null">
            AND b.status = #{status}
        </if>
        ORDER BY b.created_at DESC
    </select>

    <!-- 搜索图书 -->
    <select id="searchBooks" resultType="com.library.entity.Book">
        SELECT b.*, c.name as category_name 
        FROM books b
        LEFT JOIN categories c ON b.category_id = c.id
        WHERE b.deleted = 0 AND b.status = 1
        AND (b.title LIKE CONCAT('%', #{keyword}, '%') 
             OR b.author LIKE CONCAT('%', #{keyword}, '%')
             OR b.isbn LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY b.title
    </select>

    <!-- 获取热门图书 -->
    <select id="findPopularBooks" resultType="com.library.entity.Book">
        SELECT b.*, c.name as category_name, COUNT(br.id) as borrow_count
        FROM books b
        LEFT JOIN categories c ON b.category_id = c.id
        LEFT JOIN borrow_records br ON b.id = br.book_id
        WHERE b.deleted = 0 AND b.status = 1
        GROUP BY b.id
        ORDER BY borrow_count DESC, b.created_at DESC
        LIMIT #{limit}
    </select>

</mapper>
