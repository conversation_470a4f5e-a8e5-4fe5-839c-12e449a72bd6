package com.library.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User {

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名（唯一）
     */
    @TableField("username")
    private String username;

    /**
     * 密码（加密存储）
     */
    @JsonIgnore
    @TableField("password")
    private String password;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;

    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 用户角色
     */
    @TableField("role")
    private UserRole role;

    /**
     * 账户状态：1-正常，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 用户角色枚举
     */
    public enum UserRole {
        /**
         * 系统管理员
         */
        ADMIN("ADMIN", "系统管理员"),
        
        /**
         * 图书管理员
         */
        LIBRARIAN("LIBRARIAN", "图书管理员"),
        
        /**
         * 普通读者
         */
        READER("READER", "普通读者");

        private final String code;
        private final String description;

        UserRole(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin() {
        return UserRole.ADMIN.equals(this.role);
    }

    /**
     * 检查用户是否为图书管理员
     */
    public boolean isLibrarian() {
        return UserRole.LIBRARIAN.equals(this.role);
    }

    /**
     * 检查用户是否为普通读者
     */
    public boolean isReader() {
        return UserRole.READER.equals(this.role);
    }

    /**
     * 检查账户是否正常
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(this.status);
    }
}
