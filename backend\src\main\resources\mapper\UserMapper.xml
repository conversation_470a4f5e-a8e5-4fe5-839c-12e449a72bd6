<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.library.repository.UserRepository">

    <!-- 分页查询用户列表 -->
    <select id="findUsersWithConditions" resultType="com.library.entity.User">
        SELECT * FROM users
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (username LIKE CONCAT('%', #{keyword}, '%') 
                 OR real_name LIKE CONCAT('%', #{keyword}, '%')
                 OR email LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="role != null and role != ''">
            AND role = #{role}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDE<PERSON> BY created_at DESC
    </select>

</mapper>
