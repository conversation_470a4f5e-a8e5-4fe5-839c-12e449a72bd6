package com.library.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 预约记录实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("reservations")
public class Reservation {

    /**
     * 预约记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 图书ID
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 预约日期
     */
    @TableField("reservation_date")
    private LocalDateTime reservationDate;

    /**
     * 预约过期日期
     */
    @TableField("expire_date")
    private LocalDateTime expireDate;

    /**
     * 预约状态
     */
    @TableField("status")
    private ReservationStatus status;

    /**
     * 备注信息
     */
    @TableField("notes")
    private String notes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 用户信息（非数据库字段）
     */
    @TableField(exist = false)
    private User user;

    /**
     * 图书信息（非数据库字段）
     */
    @TableField(exist = false)
    private Book book;

    /**
     * 预约状态枚举
     */
    public enum ReservationStatus {
        /**
         * 预约中
         */
        ACTIVE("ACTIVE", "预约中"),
        
        /**
         * 已完成（已借阅）
         */
        FULFILLED("FULFILLED", "已完成"),
        
        /**
         * 已取消
         */
        CANCELLED("CANCELLED", "已取消"),
        
        /**
         * 已过期
         */
        EXPIRED("EXPIRED", "已过期");

        private final String code;
        private final String description;

        ReservationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查预约是否有效
     */
    public boolean isActive() {
        return ReservationStatus.ACTIVE.equals(this.status) && 
               !isExpired();
    }

    /**
     * 检查预约是否过期
     */
    public boolean isExpired() {
        return expireDate != null && LocalDateTime.now().isAfter(expireDate);
    }

    /**
     * 检查预约是否已完成
     */
    public boolean isFulfilled() {
        return ReservationStatus.FULFILLED.equals(this.status);
    }

    /**
     * 检查预约是否已取消
     */
    public boolean isCancelled() {
        return ReservationStatus.CANCELLED.equals(this.status);
    }

    /**
     * 取消预约
     */
    public void cancel() {
        this.status = ReservationStatus.CANCELLED;
    }

    /**
     * 完成预约
     */
    public void fulfill() {
        this.status = ReservationStatus.FULFILLED;
    }

    /**
     * 设置为过期
     */
    public void expire() {
        this.status = ReservationStatus.EXPIRED;
    }
}
