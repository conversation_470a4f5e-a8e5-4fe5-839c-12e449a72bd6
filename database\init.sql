-- 图书管理系统数据库初始化脚本
-- 创建时间: 2025-06-21
-- 数据库版本: MySQL 8.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS library_management 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE library_management;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱地址',
    phone VARCHAR(20) COMMENT '手机号码',
    role ENUM('ADMIN', 'LIBRARIAN', 'READER') NOT NULL DEFAULT 'READER' COMMENT '用户角色',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 图书分类表
CREATE TABLE IF NOT EXISTS categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(20) NOT NULL UNIQUE COMMENT '分类编码',
    description TEXT COMMENT '分类描述',
    parent_id BIGINT COMMENT '父分类ID',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    
    UNIQUE KEY uk_code (code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图书分类表';

-- 3. 图书表
CREATE TABLE IF NOT EXISTS books (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '图书ID',
    isbn VARCHAR(20) NOT NULL UNIQUE COMMENT 'ISBN号',
    title VARCHAR(200) NOT NULL COMMENT '图书标题',
    author VARCHAR(100) NOT NULL COMMENT '作者',
    publisher VARCHAR(100) NOT NULL COMMENT '出版社',
    publish_date DATE COMMENT '出版日期',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
    total_quantity INT NOT NULL DEFAULT 0 COMMENT '总数量',
    available_quantity INT NOT NULL DEFAULT 0 COMMENT '可借数量',
    description TEXT COMMENT '图书描述',
    cover_image VARCHAR(255) COMMENT '封面图片URL',
    location VARCHAR(100) COMMENT '存放位置',
    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-下架',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    
    UNIQUE KEY uk_isbn (isbn),
    INDEX idx_category_id (category_id),
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_status (status),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图书表';

-- 4. 借阅记录表
CREATE TABLE IF NOT EXISTS borrow_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '借阅记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    book_id BIGINT NOT NULL COMMENT '图书ID',
    borrow_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '借阅日期',
    due_date TIMESTAMP NOT NULL COMMENT '应还日期',
    return_date TIMESTAMP NULL COMMENT '实际归还日期',
    renew_count INT NOT NULL DEFAULT 0 COMMENT '续借次数',
    fine_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '罚金金额',
    status ENUM('BORROWED', 'RETURNED', 'OVERDUE') NOT NULL DEFAULT 'BORROWED' COMMENT '状态',
    librarian_id BIGINT NOT NULL COMMENT '办理管理员ID',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_book_id (book_id),
    INDEX idx_borrow_date (borrow_date),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (librarian_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='借阅记录表';

-- 5. 预约记录表
CREATE TABLE IF NOT EXISTS reservations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    book_id BIGINT NOT NULL COMMENT '图书ID',
    reservation_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预约日期',
    expire_date TIMESTAMP NOT NULL COMMENT '预约过期日期',
    status ENUM('ACTIVE', 'FULFILLED', 'CANCELLED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_book_id (book_id),
    INDEX idx_status (status),
    INDEX idx_expire_date (expire_date),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约记录表';

-- 插入初始数据

-- 插入默认管理员账户
INSERT INTO users (username, password, real_name, role, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.GfaAUKyG', '系统管理员', 'ADMIN', 1),
('librarian', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.GfaAUKyG', '图书管理员', 'LIBRARIAN', 1);
-- 密码都是: 123456

-- 插入默认图书分类
INSERT INTO categories (name, code, description, sort_order) VALUES
('文学', 'LIT', '文学类图书，包括小说、诗歌、散文等', 1),
('科技', 'SCI', '科学技术类图书，包括计算机、工程、医学等', 2),
('历史', 'HIS', '历史类图书，包括中外历史、传记等', 3),
('艺术', 'ART', '艺术类图书，包括绘画、音乐、设计等', 4),
('教育', 'EDU', '教育类图书，包括教材、教学参考书等', 5);

-- 插入示例图书
INSERT INTO books (isbn, title, author, publisher, publish_date, category_id, price, total_quantity, available_quantity, description, location) VALUES
('9787111234567', 'Java编程思想', 'Bruce Eckel', '机械工业出版社', '2020-01-01', 2, 89.00, 5, 5, 'Java编程经典教材', 'A区1层001'),
('9787115234568', 'Python核心编程', 'Wesley Chun', '人民邮电出版社', '2019-06-01', 2, 79.00, 3, 3, 'Python编程进阶教材', 'A区1层002'),
('9787508234569', '红楼梦', '曹雪芹', '中华书局', '2018-03-01', 1, 45.00, 10, 10, '中国古典文学名著', 'B区2层001'),
('9787020234570', '三国演义', '罗贯中', '人民文学出版社', '2017-09-01', 1, 42.00, 8, 8, '中国古典文学名著', 'B区2层002'),
('9787100234571', '中国通史', '范文澜', '人民出版社', '2019-12-01', 3, 68.00, 4, 4, '中国历史通史', 'C区3层001');

-- 创建视图：用户借阅统计
CREATE VIEW user_borrow_stats AS
SELECT 
    u.id,
    u.username,
    u.real_name,
    COUNT(br.id) as total_borrows,
    COUNT(CASE WHEN br.status = 'BORROWED' THEN 1 END) as current_borrows,
    COUNT(CASE WHEN br.status = 'OVERDUE' THEN 1 END) as overdue_borrows,
    SUM(br.fine_amount) as total_fines
FROM users u
LEFT JOIN borrow_records br ON u.id = br.user_id
WHERE u.deleted = 0
GROUP BY u.id, u.username, u.real_name;

-- 创建视图：图书借阅统计
CREATE VIEW book_borrow_stats AS
SELECT 
    b.id,
    b.isbn,
    b.title,
    b.author,
    c.name as category_name,
    b.total_quantity,
    b.available_quantity,
    COUNT(br.id) as total_borrows,
    COUNT(CASE WHEN br.status = 'BORROWED' THEN 1 END) as current_borrows
FROM books b
LEFT JOIN categories c ON b.category_id = c.id
LEFT JOIN borrow_records br ON b.id = br.book_id
WHERE b.deleted = 0
GROUP BY b.id, b.isbn, b.title, b.author, c.name, b.total_quantity, b.available_quantity;

-- 创建存储过程：自动处理逾期
DELIMITER //
CREATE PROCEDURE ProcessOverdueBooks()
BEGIN
    UPDATE borrow_records 
    SET status = 'OVERDUE'
    WHERE status = 'BORROWED' 
    AND due_date < NOW()
    AND return_date IS NULL;
END //
DELIMITER ;

-- 创建存储过程：自动处理过期预约
DELIMITER //
CREATE PROCEDURE ProcessExpiredReservations()
BEGIN
    UPDATE reservations 
    SET status = 'EXPIRED'
    WHERE status = 'ACTIVE' 
    AND expire_date < NOW();
END //
DELIMITER ;

-- 创建定时任务（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- 每小时检查一次逾期图书
-- CREATE EVENT IF NOT EXISTS check_overdue_books
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL ProcessOverdueBooks();

-- 每小时检查一次过期预约
-- CREATE EVENT IF NOT EXISTS check_expired_reservations
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL ProcessExpiredReservations();

COMMIT;
