<template>
  <div class="dashboard">
    <h1 class="page-title">系统首页</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon books">
              <el-icon size="32"><Reading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalBooks || 0 }}</div>
              <div class="stats-label">图书总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon users">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalUsers || 0 }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon borrows">
              <el-icon size="32"><DocumentCopy /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.currentBorrows || 0 }}</div>
              <div class="stats-label">当前借阅</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon overdue">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.overdueRecords || 0 }}</div>
              <div class="stats-label">逾期记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="action-buttons">
            <el-button
              v-if="userStore.hasPermission(['ADMIN', 'LIBRARIAN'])"
              type="primary"
              @click="$router.push('/books')"
            >
              <el-icon><Plus /></el-icon>
              添加图书
            </el-button>
            <el-button
              v-if="userStore.hasPermission(['ADMIN', 'LIBRARIAN'])"
              type="success"
              @click="$router.push('/borrows')"
            >
              <el-icon><DocumentAdd /></el-icon>
              借阅管理
            </el-button>
            <el-button
              v-if="userStore.hasPermission(['ADMIN'])"
              type="warning"
              @click="$router.push('/users')"
            >
              <el-icon><UserFilled /></el-icon>
              用户管理
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="system-info">
            <p><strong>当前用户：</strong>{{ userStore.userInfo?.realName }}</p>
            <p><strong>用户角色：</strong>{{ userStore.userInfo?.roleDescription }}</p>
            <p><strong>登录时间：</strong>{{ new Date().toLocaleString() }}</p>
            <p><strong>系统版本：</strong>v1.0.0</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-card class="recent-activities">
      <template #header>
        <span>最近活动</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="activity in recentActivities"
          :key="activity.id"
          :timestamp="activity.time"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const stats = ref({
  totalBooks: 0,
  totalUsers: 0,
  currentBorrows: 0,
  overdueRecords: 0
})

const recentActivities = ref([
  {
    id: 1,
    content: '系统启动成功',
    time: new Date().toLocaleString(),
    type: 'success'
  },
  {
    id: 2,
    content: '用户登录系统',
    time: new Date(Date.now() - 60000).toLocaleString(),
    type: 'primary'
  }
])

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用API获取真实数据
    // 暂时使用模拟数据
    stats.value = {
      totalBooks: 156,
      totalUsers: 89,
      currentBorrows: 23,
      overdueRecords: 5
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .page-title {
    margin-bottom: 20px;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .stats-card {
    .stats-content {
      display: flex;
      align-items: center;
      
      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        &.books {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
        
        &.users {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
        }
        
        &.borrows {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }
        
        &.overdue {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          color: white;
        }
      }
      
      .stats-info {
        .stats-number {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
        }
        
        .stats-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 20px;
    
    .action-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
    
    .system-info {
      p {
        margin: 8px 0;
        color: #606266;
        
        strong {
          color: #303133;
        }
      }
    }
  }
  
  .recent-activities {
    :deep(.el-timeline-item__timestamp) {
      color: #909399;
      font-size: 12px;
    }
  }
}
</style>
