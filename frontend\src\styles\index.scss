// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

// Element Plus 组件样式覆盖
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 18px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-button {
  border-radius: 6px;
  
  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    
    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

// 页面布局样式
.page-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .page-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

// 表单样式
.form-container {
  max-width: 600px;
  margin: 0 auto;
  
  .form-actions {
    text-align: center;
    margin-top: 30px;
    
    .el-button {
      margin: 0 10px;
    }
  }
}

// 搜索表单样式
.search-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .search-actions {
    text-align: right;
    margin-top: 15px;
    
    .el-button {
      margin-left: 10px;
    }
  }
}

// 操作按钮样式
.action-buttons {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
  }
}

// 状态标签样式
.status-tag {
  &.active {
    background-color: #f0f9ff;
    color: #1890ff;
    border: 1px solid #b3d8ff;
  }
  
  &.inactive {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffb3b3;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 15px;
  }
  
  .el-col {
    margin-bottom: 10px;
  }
}
