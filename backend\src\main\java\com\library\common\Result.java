package com.library.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 统一响应结果类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
public class Result<T> {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * 私有构造函数
     */
    private Result() {
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.code = ResultCode.SUCCESS.getCode();
        result.message = ResultCode.SUCCESS.getMessage();
        return result;
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.code = ResultCode.SUCCESS.getCode();
        result.message = ResultCode.SUCCESS.getMessage();
        result.data = data;
        return result;
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.code = ResultCode.SUCCESS.getCode();
        result.message = message;
        result.data = data;
        return result;
    }

    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        Result<T> result = new Result<>();
        result.code = ResultCode.ERROR.getCode();
        result.message = ResultCode.ERROR.getMessage();
        return result;
    }

    /**
     * 失败响应（自定义消息）
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.code = ResultCode.ERROR.getCode();
        result.message = message;
        return result;
    }

    /**
     * 失败响应（自定义码和消息）
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.code = code;
        result.message = message;
        return result;
    }

    /**
     * 失败响应（使用结果码枚举）
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        Result<T> result = new Result<>();
        result.code = resultCode.getCode();
        result.message = resultCode.getMessage();
        return result;
    }

    /**
     * 参数错误响应
     */
    public static <T> Result<T> badRequest(String message) {
        Result<T> result = new Result<>();
        result.code = ResultCode.BAD_REQUEST.getCode();
        result.message = message;
        return result;
    }

    /**
     * 未授权响应
     */
    public static <T> Result<T> unauthorized() {
        Result<T> result = new Result<>();
        result.code = ResultCode.UNAUTHORIZED.getCode();
        result.message = ResultCode.UNAUTHORIZED.getMessage();
        return result;
    }

    /**
     * 权限不足响应
     */
    public static <T> Result<T> forbidden() {
        Result<T> result = new Result<>();
        result.code = ResultCode.FORBIDDEN.getCode();
        result.message = ResultCode.FORBIDDEN.getMessage();
        return result;
    }

    /**
     * 资源不存在响应
     */
    public static <T> Result<T> notFound(String message) {
        Result<T> result = new Result<>();
        result.code = ResultCode.NOT_FOUND.getCode();
        result.message = message;
        return result;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
