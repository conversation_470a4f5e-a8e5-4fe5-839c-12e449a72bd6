# 图书管理系统

一个基于Spring Boot + Vue.js的现代化图书管理系统，采用前后端分离架构设计。

## 项目概述

本项目是系统分析与设计课程的大作业，旨在通过完整的系统开发过程，掌握结构化方法和面向对象方法在信息系统分析与设计中的应用。

### 主要功能

- 🔐 **用户管理** - 支持多角色用户注册、登录、权限控制
- 📚 **图书管理** - 图书信息的增删改查、分类管理、库存控制
- 📖 **借阅管理** - 图书借阅、归还、续借、逾期处理
- 📅 **预约管理** - 图书预约、状态跟踪、自动过期处理
- 📊 **统计报表** - 数据统计分析、可视化展示
- 🎨 **响应式界面** - 现代化UI设计，支持多设备访问

### 技术架构

#### 后端技术栈
- **框架**: Spring Boot 3.x
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus
- **构建工具**: Maven
- **Java版本**: JDK 17

#### 前端技术栈
- **框架**: Vue 3
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构

```
library-management-system/
├── backend/                    # 后端项目
│   ├── src/main/java/
│   │   └── com/library/
│   │       ├── entity/         # 实体类
│   │       ├── repository/     # 数据访问层
│   │       ├── service/        # 业务服务层
│   │       ├── controller/     # 控制器层
│   │       ├── config/         # 配置类
│   │       ├── security/       # 安全组件
│   │       ├── util/           # 工具类
│   │       ├── common/         # 通用类
│   │       └── exception/      # 异常处理
│   └── src/main/resources/
│       ├── application.yml     # 配置文件
│       └── mapper/             # MyBatis映射文件
├── frontend/                   # 前端项目
│   ├── src/
│   │   ├── api/                # API接口
│   │   ├── components/         # 通用组件
│   │   ├── layout/             # 布局组件
│   │   ├── router/             # 路由配置
│   │   ├── stores/             # 状态管理
│   │   ├── styles/             # 样式文件
│   │   └── views/              # 页面组件
│   ├── package.json
│   └── vite.config.js
├── database/                   # 数据库脚本
│   └── init.sql               # 初始化脚本
├── docs/                      # 项目文档
│   ├── 系统分析设计报告.md      # 完整的分析设计报告
│   ├── 数据库设计.md           # 数据库设计文档
│   └── API接口文档.md          # API接口文档
└── issues/                    # 任务管理
    └── 图书管理系统开发任务.md   # 开发任务记录
```

## 快速开始

### 环境要求

- JDK 17+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. 克隆项目到本地
```bash
git clone <repository-url>
cd library-management-system
```

2. 创建数据库并导入初始数据
```bash
mysql -u root -p < database/init.sql
```

3. 修改配置文件
```yaml
# backend/src/main/resources/application.yml
spring:
  datasource:
    url: **********************************************
    username: your_username
    password: your_password
```

4. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动

1. 安装依赖
```bash
cd frontend
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

前端应用将在 http://localhost:3000 启动

### 默认账户

- **管理员**: admin / 123456
- **图书管理员**: librarian / 123456

## 系统截图

### 登录界面
现代化的登录界面，支持用户身份验证

### 系统首页
直观的数据统计和快捷操作入口

### 图书管理
完整的图书CRUD功能，支持搜索和分页

### 用户管理
多角色用户管理，权限控制

## 开发文档

详细的开发文档请参考：

- [系统分析设计报告](./docs/系统分析设计报告.md) - 完整的系统分析与设计过程
- [数据库设计文档](./docs/数据库设计.md) - 数据库表结构和关系设计
- [API接口文档](./docs/API接口文档.md) - RESTful API接口规范

## AI辅助开发

本项目大量使用了AI工具辅助开发，包括：

- **Augment Agent** - 主要开发助手，负责架构设计和代码生成
- **GitHub Copilot** - 代码自动补全
- **ChatGPT** - 技术问题解答和代码审查
- **Claude** - 文档编写和逻辑分析

AI工具的使用显著提高了开发效率，详细的使用体验请参考分析设计报告第9章。

## 项目特色

### 1. 完整的开发流程
从需求分析到系统实施，完整展示了软件开发的全过程

### 2. 现代化技术栈
采用最新的技术框架，体现了现代Web开发的最佳实践

### 3. 前后端分离
清晰的架构分层，便于维护和扩展

### 4. 安全机制完善
JWT认证、角色权限控制、数据验证等安全措施

### 5. 用户体验优秀
响应式设计、友好的交互界面、完善的错误处理

### 6. AI辅助开发
展示了AI工具在现代软件开发中的应用价值

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目仅用于学习和教育目的。

## 联系方式

如有问题，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**项目统计**
- 代码行数: ~8000行
- 开发周期: 3周
- 技术栈: 10+种
- AI工具: 4种

*最后更新: 2025年6月21日*
