<template>
  <div class="books-page">
    <div class="page-header">
      <h1 class="page-title">图书管理</h1>
      <p class="page-description">管理图书信息，包括添加、编辑、删除和查询功能</p>
    </div>
    
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="图书标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入图书标题"
            clearable
          />
        </el-form-item>
        <el-form-item label="作者">
          <el-input
            v-model="searchForm.author"
            placeholder="请输入作者"
            clearable
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.categoryId"
            placeholder="请选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button
        v-if="userStore.hasPermission(['ADMIN', 'LIBRARIAN'])"
        type="primary"
        @click="handleAdd"
      >
        <el-icon><Plus /></el-icon>
        添加图书
      </el-button>
      <el-button
        v-if="userStore.hasPermission(['ADMIN', 'LIBRARIAN'])"
        type="success"
        @click="handleBatchImport"
      >
        <el-icon><Upload /></el-icon>
        批量导入
      </el-button>
      <el-button type="info" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>
    
    <!-- 图书列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="bookList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="isbn" label="ISBN" width="150" />
        <el-table-column prop="title" label="图书标题" min-width="200" />
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="publisher" label="出版社" width="150" />
        <el-table-column prop="categoryName" label="分类" width="100" />
        <el-table-column prop="price" label="价格" width="80">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="totalQuantity" label="总数量" width="80" />
        <el-table-column prop="availableQuantity" label="可借数量" width="90" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="userStore.hasPermission(['ADMIN', 'LIBRARIAN'])"
          label="操作"
          width="180"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const userStore = useUserStore()

const loading = ref(false)
const bookList = ref([])
const categories = ref([
  { id: 1, name: '文学' },
  { id: 2, name: '科技' },
  { id: 3, name: '历史' },
  { id: 4, name: '艺术' },
  { id: 5, name: '教育' }
])

const searchForm = reactive({
  title: '',
  author: '',
  categoryId: null
})

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 模拟数据
const mockBooks = [
  {
    id: 1,
    isbn: '9787111234567',
    title: 'Java编程思想',
    author: 'Bruce Eckel',
    publisher: '机械工业出版社',
    categoryName: '科技',
    price: 89.00,
    totalQuantity: 5,
    availableQuantity: 3,
    status: 1
  },
  {
    id: 2,
    isbn: '9787115234568',
    title: 'Python核心编程',
    author: 'Wesley Chun',
    publisher: '人民邮电出版社',
    categoryName: '科技',
    price: 79.00,
    totalQuantity: 3,
    availableQuantity: 2,
    status: 1
  },
  {
    id: 3,
    isbn: '9787508234569',
    title: '红楼梦',
    author: '曹雪芹',
    publisher: '中华书局',
    categoryName: '文学',
    price: 45.00,
    totalQuantity: 10,
    availableQuantity: 8,
    status: 1
  }
]

const loadBooks = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    bookList.value = mockBooks
    pagination.total = mockBooks.length
  } catch (error) {
    ElMessage.error('加载图书列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadBooks()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'categoryId' ? null : ''
  })
  handleSearch()
}

const handleAdd = () => {
  ElMessage.info('添加图书功能开发中...')
}

const handleEdit = (row) => {
  ElMessage.info(`编辑图书: ${row.title}`)
}

const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除图书《${row.title}》吗？`, '确认删除', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    loadBooks()
  })
}

const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadBooks()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadBooks()
}

onMounted(() => {
  loadBooks()
})
</script>

<style lang="scss" scoped>
.books-page {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .action-buttons {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .table-card {
    .el-pagination {
      margin-top: 20px;
      text-align: center;
    }
  }
}
</style>
