# 图书管理系统启动指南

## 快速启动步骤

### 1. 环境准备

确保已安装以下软件：
- JDK 17+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库初始化

```bash
# 登录MySQL
mysql -u root -p

# 执行初始化脚本
source database/init.sql
```

### 3. 后端启动

```bash
# 进入后端目录
cd backend

# 修改数据库配置（如需要）
# 编辑 src/main/resources/application.yml
# 修改数据库连接信息

# 启动后端服务
mvn spring-boot:run
```

后端服务启动后访问：http://localhost:8080

### 4. 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用启动后访问：http://localhost:3000

### 5. 登录测试

使用以下默认账户登录：

- **管理员账户**
  - 用户名：admin
  - 密码：123456

- **图书管理员账户**
  - 用户名：librarian
  - 密码：123456

## 注意事项

1. **数据库配置**：确保MySQL服务正在运行，并且数据库连接信息正确
2. **端口冲突**：如果8080或3000端口被占用，请修改配置文件中的端口设置
3. **网络代理**：前端开发服务器已配置代理，会自动转发API请求到后端

## 功能测试

启动成功后可以测试以下功能：

1. **用户认证**
   - 登录/退出
   - 权限控制

2. **图书管理**
   - 查看图书列表
   - 搜索图书
   - 分页功能

3. **系统导航**
   - 侧边栏菜单
   - 页面路由
   - 响应式布局

## 开发模式

如果需要进行开发，建议：

1. 使用IDE（如IntelliJ IDEA）打开后端项目
2. 使用VS Code打开前端项目
3. 开启热重载功能，修改代码后自动刷新

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查JDK版本是否为17+
   - 检查MySQL连接是否正常
   - 查看控制台错误信息

2. **前端启动失败**
   - 检查Node.js版本是否为16+
   - 删除node_modules重新安装依赖
   - 检查网络连接

3. **登录失败**
   - 确认数据库初始化是否成功
   - 检查后端服务是否正常运行
   - 查看浏览器控制台错误信息

### 日志查看

- **后端日志**：控制台输出和logs/library-management.log文件
- **前端日志**：浏览器开发者工具控制台

## API文档

后端启动后，可以访问Swagger API文档：
http://localhost:8080/swagger-ui.html

## 项目结构

详细的项目结构说明请参考README.md文件。
