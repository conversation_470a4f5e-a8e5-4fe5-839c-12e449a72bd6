<template>
  <div class="profile-page">
    <div class="page-header">
      <h1 class="page-title">个人中心</h1>
      <p class="page-description">查看和修改个人信息</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <div class="user-avatar">
            <el-avatar :size="100">
              <el-icon size="50"><UserFilled /></el-icon>
            </el-avatar>
            <h3>{{ userStore.userInfo?.realName }}</h3>
            <p>{{ userStore.userInfo?.roleDescription }}</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ userStore.userInfo?.username }}
            </el-descriptions-item>
            <el-descriptions-item label="真实姓名">
              {{ userStore.userInfo?.realName }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱地址">
              {{ userStore.userInfo?.email || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号码">
              {{ userStore.userInfo?.phone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="用户角色">
              {{ userStore.userInfo?.roleDescription }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ new Date().toLocaleDateString() }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="profile-actions">
            <el-button type="primary" @click="handleEditProfile">
              <el-icon><Edit /></el-icon>
              编辑资料
            </el-button>
            <el-button @click="handleChangePassword">
              <el-icon><Lock /></el-icon>
              修改密码
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card class="activity-card">
      <template #header>
        <span>最近活动</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="activity in activities"
          :key="activity.id"
          :timestamp="activity.time"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

const activities = ref([
  {
    id: 1,
    content: '登录系统',
    time: new Date().toLocaleString(),
    type: 'success'
  },
  {
    id: 2,
    content: '查看图书列表',
    time: new Date(Date.now() - 300000).toLocaleString(),
    type: 'primary'
  },
  {
    id: 3,
    content: '修改个人信息',
    time: new Date(Date.now() - 600000).toLocaleString(),
    type: 'info'
  }
])

const handleEditProfile = () => {
  ElMessage.info('编辑资料功能开发中...')
}

const handleChangePassword = () => {
  ElMessage.info('修改密码功能开发中...')
}
</script>

<style lang="scss" scoped>
.profile-page {
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .user-avatar {
    text-align: center;
    padding: 20px;
    
    h3 {
      margin: 15px 0 5px;
      color: #303133;
    }
    
    p {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .profile-actions {
    margin-top: 20px;
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
  
  .activity-card {
    margin-top: 20px;
  }
}
</style>
