package com.library.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Data
@Builder
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌")
    private String token;

    @Schema(description = "用户信息")
    private UserInfo user;

    /**
     * 用户信息
     */
    @Data
    @Builder
    @Schema(description = "用户信息")
    public static class UserInfo {

        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "真实姓名")
        private String realName;

        @Schema(description = "邮箱地址")
        private String email;

        @Schema(description = "手机号码")
        private String phone;

        @Schema(description = "用户角色代码")
        private String role;

        @Schema(description = "用户角色描述")
        private String roleDescription;
    }
}
