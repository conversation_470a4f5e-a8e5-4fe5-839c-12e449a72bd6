# 图书管理系统数据库设计

## 1. 数据库概述

**数据库名称：** library_management  
**数据库类型：** MySQL 8.0  
**字符集：** utf8mb4  
**排序规则：** utf8mb4_unicode_ci  

## 2. 数据表设计

### 2.1 用户表 (users)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，用户ID |
| username | VARCHAR | 50 | NOT NULL | - | 用户名，唯一 |
| password | VARCHAR | 255 | NOT NULL | - | 密码（加密存储） |
| real_name | VARCHAR | 100 | NOT NULL | - | 真实姓名 |
| email | VARCHAR | 100 | NULL | - | 邮箱地址 |
| phone | VARCHAR | 20 | NULL | - | 手机号码 |
| role | ENUM | - | NOT NULL | 'READER' | 用户角色：ADMIN, LIBRARIAN, READER |
| status | TINYINT | 1 | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_username (username)
- KEY idx_email (email)

### 2.2 图书分类表 (categories)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，分类ID |
| name | VARCHAR | 100 | NOT NULL | - | 分类名称 |
| code | VARCHAR | 20 | NOT NULL | - | 分类编码，唯一 |
| description | TEXT | - | NULL | - | 分类描述 |
| parent_id | BIGINT | - | NULL | - | 父分类ID |
| sort_order | INT | - | NOT NULL | 0 | 排序顺序 |
| status | TINYINT | 1 | NOT NULL | 1 | 状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_code (code)
- KEY idx_parent_id (parent_id)

### 2.3 图书表 (books)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，图书ID |
| isbn | VARCHAR | 20 | NOT NULL | - | ISBN号，唯一 |
| title | VARCHAR | 200 | NOT NULL | - | 图书标题 |
| author | VARCHAR | 100 | NOT NULL | - | 作者 |
| publisher | VARCHAR | 100 | NOT NULL | - | 出版社 |
| publish_date | DATE | - | NULL | - | 出版日期 |
| category_id | BIGINT | - | NOT NULL | - | 分类ID |
| price | DECIMAL | 10,2 | NOT NULL | 0.00 | 价格 |
| total_quantity | INT | - | NOT NULL | 0 | 总数量 |
| available_quantity | INT | - | NOT NULL | 0 | 可借数量 |
| description | TEXT | - | NULL | - | 图书描述 |
| cover_image | VARCHAR | 255 | NULL | - | 封面图片URL |
| location | VARCHAR | 100 | NULL | - | 存放位置 |
| status | TINYINT | 1 | NOT NULL | 1 | 状态：1-正常，0-下架 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE KEY uk_isbn (isbn)
- KEY idx_category_id (category_id)
- KEY idx_title (title)
- KEY idx_author (author)

### 2.4 借阅记录表 (borrow_records)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，借阅记录ID |
| user_id | BIGINT | - | NOT NULL | - | 用户ID |
| book_id | BIGINT | - | NOT NULL | - | 图书ID |
| borrow_date | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 借阅日期 |
| due_date | TIMESTAMP | - | NOT NULL | - | 应还日期 |
| return_date | TIMESTAMP | - | NULL | - | 实际归还日期 |
| renew_count | INT | - | NOT NULL | 0 | 续借次数 |
| fine_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 罚金金额 |
| status | ENUM | - | NOT NULL | 'BORROWED' | 状态：BORROWED, RETURNED, OVERDUE |
| librarian_id | BIGINT | - | NOT NULL | - | 办理管理员ID |
| notes | TEXT | - | NULL | - | 备注信息 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- KEY idx_user_id (user_id)
- KEY idx_book_id (book_id)
- KEY idx_borrow_date (borrow_date)
- KEY idx_status (status)

### 2.5 预约记录表 (reservations)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，预约记录ID |
| user_id | BIGINT | - | NOT NULL | - | 用户ID |
| book_id | BIGINT | - | NOT NULL | - | 图书ID |
| reservation_date | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 预约日期 |
| expire_date | TIMESTAMP | - | NOT NULL | - | 预约过期日期 |
| status | ENUM | - | NOT NULL | 'ACTIVE' | 状态：ACTIVE, FULFILLED, CANCELLED, EXPIRED |
| notes | TEXT | - | NULL | - | 备注信息 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- KEY idx_user_id (user_id)
- KEY idx_book_id (book_id)
- KEY idx_status (status)

## 3. 外键约束

```sql
-- 图书表外键
ALTER TABLE books ADD CONSTRAINT fk_books_category 
FOREIGN KEY (category_id) REFERENCES categories(id);

-- 借阅记录表外键
ALTER TABLE borrow_records ADD CONSTRAINT fk_borrow_user 
FOREIGN KEY (user_id) REFERENCES users(id);

ALTER TABLE borrow_records ADD CONSTRAINT fk_borrow_book 
FOREIGN KEY (book_id) REFERENCES books(id);

ALTER TABLE borrow_records ADD CONSTRAINT fk_borrow_librarian 
FOREIGN KEY (librarian_id) REFERENCES users(id);

-- 预约记录表外键
ALTER TABLE reservations ADD CONSTRAINT fk_reservation_user 
FOREIGN KEY (user_id) REFERENCES users(id);

ALTER TABLE reservations ADD CONSTRAINT fk_reservation_book 
FOREIGN KEY (book_id) REFERENCES books(id);
```

## 4. 初始化数据

### 4.1 默认管理员账户
```sql
INSERT INTO users (username, password, real_name, role, status) 
VALUES ('admin', '$2a$10$encrypted_password', '系统管理员', 'ADMIN', 1);
```

### 4.2 默认图书分类
```sql
INSERT INTO categories (name, code, description, sort_order) VALUES
('文学', 'LIT', '文学类图书', 1),
('科技', 'SCI', '科学技术类图书', 2),
('历史', 'HIS', '历史类图书', 3),
('艺术', 'ART', '艺术类图书', 4);
```

## 5. 数据库性能优化

### 5.1 索引策略
- 为经常查询的字段创建索引
- 为外键字段创建索引
- 避免过多索引影响写入性能

### 5.2 分区策略
- 借阅记录表可按时间分区
- 大数据量时考虑水平分表

### 5.3 缓存策略
- 图书分类信息缓存
- 热门图书信息缓存
- 用户会话信息缓存
