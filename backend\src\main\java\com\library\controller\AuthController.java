package com.library.controller;

import com.library.common.Result;
import com.library.dto.request.LoginRequest;
import com.library.dto.request.RegisterRequest;
import com.library.dto.response.LoginResponse;
import com.library.entity.User;
import com.library.service.UserService;
import com.library.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final UserService userService;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: {}", request.getUsername());
        
        // 验证用户凭据
        User user = userService.login(request.getUsername(), request.getPassword());
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getRole().getCode());
        
        // 构建响应
        LoginResponse response = LoginResponse.builder()
                .token(token)
                .user(LoginResponse.UserInfo.builder()
                        .id(user.getId())
                        .username(user.getUsername())
                        .realName(user.getRealName())
                        .email(user.getEmail())
                        .role(user.getRole().getCode())
                        .roleDescription(user.getRole().getDescription())
                        .build())
                .build();
        
        log.info("用户登录成功: {}", request.getUsername());
        return Result.success("登录成功", response);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册")
    public Result<Void> register(@Valid @RequestBody RegisterRequest request) {
        log.info("用户注册请求: {}", request.getUsername());
        
        // 构建用户对象
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(request.getPassword());
        user.setRealName(request.getRealName());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        
        // 注册用户
        userService.register(user);
        
        log.info("用户注册成功: {}", request.getUsername());
        return Result.success("注册成功");
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    @Operation(summary = "用户退出", description = "用户退出登录")
    public Result<Void> logout(@RequestHeader(value = "Authorization", required = false) String authHeader) {
        // 从请求头中解析令牌
        String token = jwtUtil.resolveToken(authHeader);
        if (token != null) {
            String username = jwtUtil.getUsernameFromToken(token);
            log.info("用户退出登录: {}", username);
            
            // TODO: 将令牌加入黑名单（可选实现）
            // tokenBlacklistService.addToBlacklist(token);
        }
        
        return Result.success("退出成功");
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "刷新访问令牌")
    public Result<LoginResponse> refreshToken(@RequestHeader("Authorization") String authHeader) {
        // 解析当前令牌
        String token = jwtUtil.resolveToken(authHeader);
        if (token == null || !jwtUtil.validateTokenFormat(token)) {
            return Result.error("无效的令牌");
        }
        
        // 刷新令牌
        String newToken = jwtUtil.refreshToken(token);
        if (newToken == null) {
            return Result.error("令牌刷新失败");
        }
        
        // 获取用户信息
        String username = jwtUtil.getUsernameFromToken(newToken);
        User user = userService.findByUsername(username);
        
        // 构建响应
        LoginResponse response = LoginResponse.builder()
                .token(newToken)
                .user(LoginResponse.UserInfo.builder()
                        .id(user.getId())
                        .username(user.getUsername())
                        .realName(user.getRealName())
                        .email(user.getEmail())
                        .role(user.getRole().getCode())
                        .roleDescription(user.getRole().getDescription())
                        .build())
                .build();
        
        return Result.success("令牌刷新成功", response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户", description = "获取当前登录用户信息")
    public Result<LoginResponse.UserInfo> getCurrentUser(@RequestHeader("Authorization") String authHeader) {
        // 解析令牌获取用户信息
        String token = jwtUtil.resolveToken(authHeader);
        String username = jwtUtil.getUsernameFromToken(token);
        
        User user = userService.findByUsername(username);
        
        LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                .id(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .role(user.getRole().getCode())
                .roleDescription(user.getRole().getDescription())
                .build();
        
        return Result.success(userInfo);
    }
}
